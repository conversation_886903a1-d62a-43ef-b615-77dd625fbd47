_pillow_heif.cpython-311-darwin.so,sha256=oXdqpZJA_PQOIyGOawHH8rSNxcrpUTAHrXZC6PdFkoQ,135808
pillow_heif-0.22.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pillow_heif-0.22.0.dist-info/LICENSE.txt,sha256=niY18VWwCvWkbLLyybBSBy7FRtWYN-5078nLosu4Pz0,1546
pillow_heif-0.22.0.dist-info/LICENSES_bundled.txt,sha256=IAzhZEIafsbwdC3KzxAFUYR617FqxY5vMN0PNv2Pz34,1015
pillow_heif-0.22.0.dist-info/METADATA,sha256=T21H931F7jUdiNUKlrMY8zbfwUe7SKOyDf-YopqTCD8,9579
pillow_heif-0.22.0.dist-info/RECORD,,
pillow_heif-0.22.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pillow_heif-0.22.0.dist-info/WHEEL,sha256=oAA21f10FAOZAvD2S-cQJ-r1UWuJ8bLdIgRQdrmKXEA,109
pillow_heif-0.22.0.dist-info/top_level.txt,sha256=iX-kTFDQPRwRFsqj5c6PgZgetruemjt6Xkw9Dgb978o,25
pillow_heif-0.22.0.dist-info/zip-safe,sha256=AbpHGcgLb-kRsJGnwFEktk7uzpZOCcBY74-YBdrKVGs,1
pillow_heif/.dylibs/libaom.3.12.0.dylib,sha256=IoYAArrBUVRSV4qN2FHLZgHVJ57IdaD74Hi5kiWNeBA,3905008
pillow_heif/.dylibs/libde265.0.dylib,sha256=3NYHZdpF6U1RZxV99z2aGvtdmZ8BdzncBhFfqVlZTws,438448
pillow_heif/.dylibs/libheif.1.19.7.dylib,sha256=8RzicBM1g2NjgMADbQ6xuIDlUk6fqHhFu4xAMslNcj0,1133856
pillow_heif/.dylibs/libvmaf.3.dylib,sha256=shoOHiY7dqXyC1Vz8VKfSDVNJrGoKA-VtvXkoRVWo6w,748064
pillow_heif/.dylibs/libx265.215.dylib,sha256=d5gq9O-cqLpjSxoWxPTb_Y9lQlvcxhVNCs9L0ExpC0U,4923728
pillow_heif/AvifImagePlugin.py,sha256=MVq-VlOVLygXsmO69L-OWWSzhS4nUJgbS6fNy0etIpc,136
pillow_heif/HeifImagePlugin.py,sha256=kaauUwY0yq1CcomzIZogx2UMVTUm_Fm6QMIp0bQIS8w,135
pillow_heif/__init__.py,sha256=FESd-e0N4ySGu3EUsDadPEEA4nDzRrpzTYyawzHTdJA,679
pillow_heif/__pycache__/AvifImagePlugin.cpython-311.pyc,,
pillow_heif/__pycache__/HeifImagePlugin.cpython-311.pyc,,
pillow_heif/__pycache__/__init__.cpython-311.pyc,,
pillow_heif/__pycache__/_deffered_error.cpython-311.pyc,,
pillow_heif/__pycache__/_lib_info.cpython-311.pyc,,
pillow_heif/__pycache__/_version.cpython-311.pyc,,
pillow_heif/__pycache__/as_plugin.cpython-311.pyc,,
pillow_heif/__pycache__/constants.cpython-311.pyc,,
pillow_heif/__pycache__/heif.cpython-311.pyc,,
pillow_heif/__pycache__/misc.cpython-311.pyc,,
pillow_heif/__pycache__/options.cpython-311.pyc,,
pillow_heif/_deffered_error.py,sha256=e02pV5yE-TGboDG5YTYLEgrfD3o0FnEGpK5WBJUpv_g,325
pillow_heif/_lib_info.py,sha256=-xrH5BOCZy-nMwM6xJO1f0SlL2r6CIJtubwoQ5quZOI,1001
pillow_heif/_version.py,sha256=6ijxUM7E1UPgaBZGK5Bc7UY8cU_4X5y4roXL4jCAXXQ,62
pillow_heif/as_plugin.py,sha256=2uUGCfFDe5TZReN3vzarYHw1clC-GF6iW6fwKKgoVDA,10561
pillow_heif/constants.py,sha256=ARH-qzr8dcMutoeUN1y7N8BjdHDxTWt7MI8GZqgDulc,5065
pillow_heif/heif.py,sha256=vchE36uLfrk_1H9gvnavOuBGnnsT2rsd6ZYZNeutSWU,23971
pillow_heif/misc.py,sha256=JrS6RIuzhLGX2xJokeYkkmivz5SVtgsdq9JxmcodrYY,20508
pillow_heif/options.py,sha256=jfkdF3qW9cOBMVtU3VKG14BLkhaAjdhdD-SMyFSfq9U,3140
