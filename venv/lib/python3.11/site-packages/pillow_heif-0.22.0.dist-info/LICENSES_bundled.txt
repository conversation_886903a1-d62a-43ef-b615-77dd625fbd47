License for "pillow-heif" binary wheels: GPLv2, due to base library licenses.

Binary wheels combine several license-compatible libraries. Here they are listed.

Name: libheif
License: LGPLv3
Files: libheif.[dylib|so|dll]
  For details, see https://github.com/strukturag/libheif/tree/v1.18.1/COPYING
  Source code: https://github.com/strukturag/libheif/tree/v1.18.1

Name: libde265
License: LGPLv3
Files: libde265.[dylib|so|dll]
  For details, see https://github.com/strukturag/libde265/tree/v1.0.15/COPYING
  Source code: https://github.com/strukturag/libde265/tree/v1.0.15

Name: x265
License: GPLv2
Files: libx265.[dylib|so|dll]
  For details, see https://bitbucket.org/multicoreware/x265_git/src/Release_3.4/COPYING
  Source code: https://bitbucket.org/multicoreware/x265_git/src/Release_3.4

Name: libaom
License: BSD 3-Clause
Files: libaom.[dylib|so|dll]
  For details, see https://aomedia.googlesource.com/aom/+/refs/tags/v3.6.1/LICENSE
  Source code: https://aomedia.googlesource.com/aom/+/refs/tags/v3.6.1
