#!/usr/bin/env python3
"""
Installation script for Image to PDF Merger dependencies
========================================================
This script installs the required packages for the Image to PDF Merger,
including HEIC/HEIF support.

Usage:
python install_dependencies.py
"""

import subprocess
import sys
import platform

def run_command(command):
    """Run a command and return success status"""
    try:
        print(f"Running: {command}")
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print(f"✓ Success: {command}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed: {command}")
        print(f"Error: {e.stderr}")
        return False

def install_packages():
    """Install required packages"""
    print("Image to PDF Merger - Dependency Installation")
    print("=" * 50)
    
    # Basic packages
    basic_packages = [
        "Pillow",
        "reportlab"
    ]
    
    # HEIC support package
    heic_package = "pillow-heif"
    
    print("\n1. Installing basic packages...")
    for package in basic_packages:
        success = run_command(f"pip install {package}")
        if not success:
            print(f"Failed to install {package}. Please install manually.")
            return False
    
    print("\n2. Installing HEIC/HEIF support...")
    
    # Check platform for HEIC support
    system = platform.system().lower()
    
    if system == "darwin":  # macOS
        print("Detected macOS - Installing HEIC support...")
        success = run_command(f"pip install {heic_package}")
        if not success:
            print("Failed to install HEIC support. You may need to install libheif first:")
            print("brew install libheif")
            print(f"Then run: pip install {heic_package}")
    
    elif system == "linux":
        print("Detected Linux - Installing HEIC support...")
        print("Note: You may need to install libheif development packages first:")
        print("Ubuntu/Debian: sudo apt-get install libheif-dev")
        print("CentOS/RHEL: sudo yum install libheif-devel")
        print("Arch: sudo pacman -S libheif")
        
        success = run_command(f"pip install {heic_package}")
        if not success:
            print("Failed to install HEIC support. Please install libheif-dev first.")
    
    elif system == "windows":
        print("Detected Windows - Installing HEIC support...")
        success = run_command(f"pip install {heic_package}")
        if not success:
            print("Failed to install HEIC support on Windows.")
            print("You may need to install Visual C++ build tools.")
    
    else:
        print(f"Unknown system: {system}")
        print("Attempting to install HEIC support anyway...")
        success = run_command(f"pip install {heic_package}")
    
    print("\n" + "=" * 50)
    print("Installation Summary:")
    print("✓ Pillow (image processing)")
    print("✓ ReportLab (PDF generation)")
    
    if success:
        print("✓ pillow-heif (HEIC/HEIF support)")
        print("\n🎉 All dependencies installed successfully!")
        print("You can now use the Image to PDF Merger with HEIC support.")
    else:
        print("⚠ pillow-heif (HEIC/HEIF support) - Failed")
        print("\n⚠ Basic functionality available, but HEIC files won't be supported.")
        print("To add HEIC support later, install the required system libraries and run:")
        print(f"pip install {heic_package}")
    
    return True

def test_installation():
    """Test if packages are properly installed"""
    print("\n" + "=" * 50)
    print("Testing Installation...")
    
    try:
        from PIL import Image, ImageOps
        print("✓ Pillow imported successfully")
    except ImportError:
        print("✗ Pillow import failed")
        return False
    
    try:
        from reportlab.pdfgen import canvas
        print("✓ ReportLab imported successfully")
    except ImportError:
        print("✗ ReportLab import failed")
        return False
    
    try:
        from pillow_heif import register_heif_opener
        register_heif_opener()
        print("✓ HEIC/HEIF support available")
    except ImportError:
        print("⚠ HEIC/HEIF support not available")
    
    print("\n🎉 Installation test completed!")
    return True

if __name__ == "__main__":
    print("Starting dependency installation...")
    
    if install_packages():
        test_installation()
        print("\nYou can now run the Image to PDF Merger:")
        print("python image_to_pdf_merger.py")
    else:
        print("\nInstallation failed. Please check the errors above.")
        sys.exit(1)
