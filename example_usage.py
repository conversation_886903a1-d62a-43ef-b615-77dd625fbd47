#!/usr/bin/env python3
"""
Example usage of the Image to PDF Merger with HEIC support
==========================================================
This script demonstrates how to use the ImageToPDFMerger class
to convert HEIC and other image formats to PDF.
"""

import os
from pathlib import Path
from image_to_pdf_merger import ImageToPDFMerger

def example_basic_usage():
    """Basic example of merging images to PDF"""
    print("Example 1: Basic Usage")
    print("-" * 30)
    
    # Create merger instance
    merger = ImageToPDFMerger(
        page_size='a4',      # A4 page size
        margin=0.5,          # 0.5 inch margins
        quality=90           # High quality JPEG compression
    )
    
    # Example: Merge all images from a directory
    source_directory = "path/to/your/images"  # Change this to your image directory
    output_pdf = "merged_images.pdf"
    
    if os.path.exists(source_directory):
        success = merger.merge_images_to_pdf(
            source_path=source_directory,
            output_path=output_pdf,
            recursive=True,      # Search subdirectories
            sort_by='name'       # Sort by filename
        )
        
        if success:
            print(f"✓ PDF created: {output_pdf}")
        else:
            print("✗ Failed to create PDF")
    else:
        print(f"Directory not found: {source_directory}")
        print("Please update the source_directory path in this script")

def example_heic_specific():
    """Example specifically for HEIC files"""
    print("\nExample 2: HEIC Files")
    print("-" * 30)
    
    # Create merger with settings optimized for phone photos
    merger = ImageToPDFMerger(
        page_size='a4',
        margin=0.25,         # Smaller margins for photos
        quality=85           # Good balance of quality and file size
    )
    
    # Example HEIC files (typically from iPhone)
    heic_files = [
        "IMG_001.HEIC",
        "IMG_002.HEIC", 
        "IMG_003.HEIC"
    ]
    
    # Check if any HEIC files exist
    existing_files = [f for f in heic_files if os.path.exists(f)]
    
    if existing_files:
        print(f"Found {len(existing_files)} HEIC files")
        
        # Create a temporary directory with the files
        temp_dir = "temp_heic_images"
        os.makedirs(temp_dir, exist_ok=True)
        
        # You would copy your HEIC files to temp_dir here
        # For this example, we'll just show the process
        
        success = merger.merge_images_to_pdf(
            source_path=temp_dir,
            output_path="heic_photos.pdf",
            sort_by='date'       # Sort by file date (good for photos)
        )
        
        if success:
            print("✓ HEIC photos converted to PDF successfully!")
    else:
        print("No HEIC files found in current directory")
        print("Place some .HEIC files in this directory to test")

def example_mixed_formats():
    """Example with mixed image formats including HEIC"""
    print("\nExample 3: Mixed Formats")
    print("-" * 30)
    
    merger = ImageToPDFMerger(page_size='letter', quality=80)
    
    # Create a list of different image formats
    mixed_formats = [
        "photo1.jpg",
        "photo2.png", 
        "photo3.HEIC",
        "photo4.heif",
        "photo5.tiff"
    ]
    
    print("This example would process these formats:")
    for fmt in mixed_formats:
        ext = Path(fmt).suffix.upper()
        print(f"  - {fmt} ({ext} format)")
    
    print("\nThe merger automatically handles:")
    print("  📱 HEIC/HEIF files from phones")
    print("  🖼️  Standard formats (JPG, PNG, etc.)")
    print("  🔄 Auto-rotation based on EXIF data")
    print("  📄 Proper sizing for PDF pages")

def example_programmatic_usage():
    """Example of using the merger programmatically"""
    print("\nExample 4: Programmatic Usage")
    print("-" * 30)
    
    # Create merger
    merger = ImageToPDFMerger()
    
    # Get image files from a directory
    image_directory = "."  # Current directory
    image_files = merger.get_image_files(image_directory, recursive=False)
    
    print(f"Found {len(image_files)} image files:")
    for img_file in image_files:
        print(f"  - {img_file.name}")
    
    if image_files:
        # Process each image individually (for custom processing)
        processed_images = []
        for img_path in image_files:
            processed_img = merger.process_image(img_path)
            if processed_img:
                processed_images.append((img_path, processed_img))
        
        print(f"Successfully processed {len(processed_images)} images")
        
        # You could then create a custom PDF with these processed images
        # This gives you full control over the PDF creation process

def main():
    """Run all examples"""
    print("Image to PDF Merger - HEIC Support Examples")
    print("=" * 50)
    
    # Check if the main module is available
    try:
        from image_to_pdf_merger import ImageToPDFMerger
        print("✓ ImageToPDFMerger module loaded successfully")
    except ImportError:
        print("✗ Cannot import ImageToPDFMerger")
        print("Make sure image_to_pdf_merger.py is in the same directory")
        return
    
    # Run examples
    example_basic_usage()
    example_heic_specific()
    example_mixed_formats()
    example_programmatic_usage()
    
    print("\n" + "=" * 50)
    print("Examples completed!")
    print("\nTo use with your own images:")
    print("1. Install dependencies: python install_dependencies.py")
    print("2. Run interactively: python image_to_pdf_merger.py")
    print("3. Or use command line: python image_to_pdf_merger.py /path/to/images")

if __name__ == "__main__":
    main()
