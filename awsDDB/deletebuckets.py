import boto3
from botocore.exceptions import ClientError

# ======== ENTER YOUR CREDENTIALS HERE ========
aws_access_key_id = "********************"
aws_secret_access_key = "yramcAAoazvKbaMZe4Wk99mtgFzxlRjtm9TVRI5H"
aws_region = "us-east-1"  # or your preferred region
# ==============================================


def delete_all_objects(bucket_name, s3):
    try:
        bucket_versioning = s3.BucketVersioning(bucket_name)
        if bucket_versioning.status == 'Enabled':
            print(f"Deleting all versions in versioned bucket: {bucket_name}")
            for obj_version in s3.Bucket(bucket_name).object_versions.all():
                obj_version.delete()
        else:
            print(f"Deleting all objects in bucket: {bucket_name}")
            for obj in s3.Bucket(bucket_name).objects.all():
                obj.delete()
    except ClientError as e:
        print(f"Error deleting objects in {bucket_name}: {e}")


def delete_all_buckets():
    session = boto3.Session(
        aws_access_key_id=aws_access_key_id,
        aws_secret_access_key=aws_secret_access_key,
        region_name=aws_region
    )

    s3 = session.resource('s3')
    s3_client = session.client('s3')

    for bucket in s3.buckets.all():
        bucket_name = bucket.name
        print(f"\nProcessing bucket: {bucket_name}")

        try:
            delete_all_objects(bucket_name, s3)
            print(f"Deleting bucket: {bucket_name}")
            s3_client.delete_bucket(Bucket=bucket_name)
        except ClientError as e:
            print(f"Failed to delete bucket {bucket_name}: {e}")


if __name__ == "__main__":
    confirm = input(
        "This will DELETE ALL S3 BUCKETS in your AWS account. Type 'DELETE' to continue: ")
    if confirm == 'DELETE':
        delete_all_buckets()
    else:
        print("Operation cancelled.")
