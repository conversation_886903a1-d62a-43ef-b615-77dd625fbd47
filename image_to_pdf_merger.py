#!/usr/bin/env python3
"""
Image to PDF Merger
===================
This script merges multiple images into a single PDF file.
Supports various image formats: JPG, JPEG, PNG, BMP, TIFF, GIF, WEBP, HEIC, HEIF

Features:
- Merge images from a directory or specific file list
- Resize images to fit PDF pages
- Maintain aspect ratio
- Sort images by name or date
- Add margins and padding
- Compress output PDF
- Support for Apple HEIC/HEIF format

Requirements:
pip install Pillow reportlab pillow-heif

Usage:
python image_to_pdf_merger.py
"""

import os
import sys
from pathlib import Path
from datetime import datetime
from typing import List, Tuple, Optional
import argparse

try:
    from PIL import Image, ImageOps
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter, A4, legal
    from reportlab.lib.units import inch

    # Import HEIC support
    try:
        from pillow_heif import register_heif_opener
        register_heif_opener()
        HEIC_SUPPORT = True
        print("✓ HEIC/HEIF support enabled")
    except ImportError:
        HEIC_SUPPORT = False
        print("⚠ HEIC/HEIF support not available. Install with: pip install pillow-heif")

except ImportError as e:
    print(f"Missing required packages. Please install them:")
    print("pip install Pillow reportlab pillow-heif")
    sys.exit(1)


class ImageToPDFMerger:
    """Class to handle image to PDF conversion and merging"""

    # Supported image formats
    SUPPORTED_FORMATS = {'.jpg', '.jpeg', '.png',
                         '.bmp', '.tiff', '.tif', '.gif', '.webp', '.heic', '.heif'}

    # Page size options
    PAGE_SIZES = {
        'letter': letter,
        'a4': A4,
        'legal': legal
    }

    def __init__(self, page_size='a4', margin=0.5, quality=85):
        """
        Initialize the merger

        Args:
            page_size (str): Page size ('letter', 'a4', 'legal')
            margin (float): Margin in inches
            quality (int): JPEG quality for compression (1-100)
        """
        self.page_size = self.PAGE_SIZES.get(page_size.lower(), A4)
        self.margin = margin * inch
        self.quality = quality
        self.page_width = self.page_size[0] - (2 * self.margin)
        self.page_height = self.page_size[1] - (2 * self.margin)

    def get_image_files(self, source_path: str, recursive: bool = False) -> List[Path]:
        """
        Get list of image files from directory or file list

        Args:
            source_path (str): Directory path or file path
            recursive (bool): Search subdirectories recursively

        Returns:
            List[Path]: List of image file paths
        """
        source = Path(source_path)
        image_files = []

        if source.is_file():
            if source.suffix.lower() in self.SUPPORTED_FORMATS:
                image_files.append(source)
        elif source.is_dir():
            pattern = "**/*" if recursive else "*"
            for file_path in source.glob(pattern):
                if file_path.is_file() and file_path.suffix.lower() in self.SUPPORTED_FORMATS:
                    image_files.append(file_path)
        else:
            raise ValueError(f"Invalid source path: {source_path}")

        return sorted(image_files)

    def calculate_image_size(self, image: Image.Image) -> Tuple[float, float]:
        """
        Calculate the size to fit image within page bounds while maintaining aspect ratio

        Args:
            image (Image.Image): PIL Image object

        Returns:
            Tuple[float, float]: (width, height) in points
        """
        img_width, img_height = image.size
        img_ratio = img_width / img_height
        page_ratio = self.page_width / self.page_height

        if img_ratio > page_ratio:
            # Image is wider, fit to page width
            new_width = self.page_width
            new_height = self.page_width / img_ratio
        else:
            # Image is taller, fit to page height
            new_height = self.page_height
            new_width = self.page_height * img_ratio

        return new_width, new_height

    def process_image(self, image_path: Path) -> Optional[Image.Image]:
        """
        Process and prepare image for PDF

        Args:
            image_path (Path): Path to image file

        Returns:
            Optional[Image.Image]: Processed image or None if error
        """
        try:
            print(f"Processing: {image_path.name}")

            # Check if it's a HEIC file and if we have support
            file_ext = image_path.suffix.lower()
            if file_ext in {'.heic', '.heif'} and not HEIC_SUPPORT:
                print(
                    f"  ⚠ HEIC/HEIF file detected but support not available: {image_path.name}")
                print("  Install pillow-heif: pip install pillow-heif")
                return None

            # Open and convert image
            image = Image.open(image_path)

            # For HEIC files, ensure we get the best quality
            if file_ext in {'.heic', '.heif'}:
                print(f"  📱 Processing HEIC/HEIF file: {image_path.name}")

            # Convert to RGB if necessary (for PDF compatibility)
            if image.mode in ('RGBA', 'LA', 'P'):
                # Create white background for transparent images
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'P':
                    image = image.convert('RGBA')
                background.paste(image, mask=image.split()
                                 [-1] if image.mode in ('RGBA', 'LA') else None)
                image = background
            elif image.mode != 'RGB':
                image = image.convert('RGB')

            # Auto-rotate based on EXIF data (important for HEIC files from phones)
            image = ImageOps.exif_transpose(image)

            print(f"  ✓ Processed: {image.size[0]}x{image.size[1]} pixels")
            return image

        except Exception as e:
            print(f"  ✗ Error processing {image_path.name}: {e}")
            if file_ext in {'.heic', '.heif'}:
                print(
                    "  💡 Tip: Make sure pillow-heif is installed: pip install pillow-heif")
            return None

    def create_pdf_from_images(self, image_paths: List[Path], output_path: str,
                               sort_by: str = 'name') -> bool:
        """
        Create PDF from list of image files

        Args:
            image_paths (List[Path]): List of image file paths
            output_path (str): Output PDF file path
            sort_by (str): Sort method ('name', 'date', 'size')

        Returns:
            bool: True if successful, False otherwise
        """
        if not image_paths:
            print("No image files found!")
            return False

        # Sort images
        if sort_by == 'date':
            image_paths.sort(key=lambda x: x.stat().st_mtime)
        elif sort_by == 'size':
            image_paths.sort(key=lambda x: x.stat().st_size)
        else:  # sort by name (default)
            image_paths.sort()

        print(f"Creating PDF with {len(image_paths)} images...")
        print(f"Output: {output_path}")

        try:
            # Create PDF canvas
            c = canvas.Canvas(output_path, pagesize=self.page_size)

            for i, image_path in enumerate(image_paths, 1):
                print(
                    f"Adding image {i}/{len(image_paths)}: {image_path.name}")

                # Process image
                image = self.process_image(image_path)
                if image is None:
                    continue

                # Calculate size and position
                img_width, img_height = self.calculate_image_size(image)
                x = (self.page_size[0] - img_width) / 2
                y = (self.page_size[1] - img_height) / 2

                # Save image temporarily for reportlab
                temp_path = f"temp_image_{i}.jpg"
                image.save(temp_path, "JPEG",
                           quality=self.quality, optimize=True)

                # Add image to PDF
                c.drawImage(temp_path, x, y, width=img_width,
                            height=img_height)

                # Clean up temporary file
                os.remove(temp_path)

                # Add new page if not the last image
                if i < len(image_paths):
                    c.showPage()

            # Save PDF
            c.save()
            print(f"✓ PDF created successfully: {output_path}")
            return True

        except Exception as e:
            print(f"Error creating PDF: {e}")
            return False

    def merge_images_to_pdf(self, source_path: str, output_path: str = None,
                            recursive: bool = False, sort_by: str = 'name') -> bool:
        """
        Main method to merge images to PDF

        Args:
            source_path (str): Source directory or file path
            output_path (str): Output PDF path (optional)
            recursive (bool): Search subdirectories
            sort_by (str): Sort method ('name', 'date', 'size')

        Returns:
            bool: True if successful, False otherwise
        """
        # Get image files
        image_files = self.get_image_files(source_path, recursive)

        if not image_files:
            print(f"No supported image files found in: {source_path}")
            return False

        # Generate output filename if not provided
        if output_path is None:
            source_name = Path(source_path).stem if Path(
                source_path).is_file() else Path(source_path).name
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"{source_name}_merged_{timestamp}.pdf"

        # Create PDF
        return self.create_pdf_from_images(image_files, output_path, sort_by)


def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(
        description="Merge images into a PDF file")
    parser.add_argument("source", help="Source directory or image file")
    parser.add_argument("-o", "--output", help="Output PDF file path")
    parser.add_argument("-r", "--recursive", action="store_true",
                        help="Search subdirectories recursively")
    parser.add_argument("--page-size", choices=['letter', 'a4', 'legal'],
                        default='a4', help="PDF page size")
    parser.add_argument("--margin", type=float, default=0.5,
                        help="Page margin in inches")
    parser.add_argument("--quality", type=int, default=85,
                        help="JPEG quality (1-100)")
    parser.add_argument("--sort", choices=['name', 'date', 'size'],
                        default='name', help="Sort images by")

    args = parser.parse_args()

    # Create merger instance
    merger = ImageToPDFMerger(
        page_size=args.page_size,
        margin=args.margin,
        quality=args.quality
    )

    # Merge images to PDF
    success = merger.merge_images_to_pdf(
        source_path=args.source,
        output_path=args.output,
        recursive=args.recursive,
        sort_by=args.sort
    )

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    # Interactive mode if no command line arguments
    if len(sys.argv) == 1:
        print("Image to PDF Merger")
        print("=" * 30)

        # Get source path
        source = input("Enter source directory or image file path: ").strip()
        if not source:
            print("No source path provided!")
            sys.exit(1)

        # Get output path
        output = input(
            "Enter output PDF path (press Enter for auto-generated): ").strip()
        output = output if output else None

        # Get options
        recursive = input(
            "Search subdirectories recursively? (y/N): ").strip().lower() == 'y'

        print("\nPage size options: letter, a4, legal")
        page_size = input("Enter page size (default: a4): ").strip() or 'a4'

        print("\nSort options: name, date, size")
        sort_by = input("Sort images by (default: name): ").strip() or 'name'

        # Create merger and process
        merger = ImageToPDFMerger(page_size=page_size)
        merger.merge_images_to_pdf(source, output, recursive, sort_by)
    else:
        main()
